package com.zenyte.game.model.ui.testinterfaces;

import com.near_reality.cache.interfaces.teleports.Category;
import com.near_reality.cache.interfaces.teleports.TeleportsList;
import com.zenyte.game.GameInterface;
import com.zenyte.game.model.ui.Interface;
import com.zenyte.game.util.AccessMask;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.world.entity.player.Skills;
import mgi.types.config.enums.Enums;

/**
 * Custom interface that displays skill options (View Guide / Teleport) as clickable buttons
 * instead of using dialogues, allowing teleport interfaces to remain clickable.
 */
public class SkillOptionsInterface extends Interface {
    
    private int currentSkill = -1;
    
    /**
     * Opens the skill options interface for a specific skill
     */
    public void openForSkill(Player player, int skill) {
        this.currentSkill = skill;
        player.getInterfaceHandler().sendInterface(this);
        
        // Set up the interface text and buttons
        String skillName = Skills.getSkillName(skill);
        player.getPacketDispatcher().sendComponentText(getInterface(), 2, "Options for " + skillName);
        player.getPacketDispatcher().sendComponentText(getInterface(), 4, "View Skill Guide");
        player.getPacketDispatcher().sendComponentText(getInterface(), 6, "Teleport");
        
        // Make buttons clickable
        player.getPacketDispatcher().sendComponentSettings(getInterface(), 4, 0, 0, AccessMask.CLICK_OP1);
        player.getPacketDispatcher().sendComponentSettings(getInterface(), 6, 0, 0, AccessMask.CLICK_OP1);
    }
    
    /**
     * Handles viewing the skill guide for the current skill
     */
    private void handleSkillGuide(Player player) {
        if (currentSkill == -1) return;
        
        player.getInterfaceHandler().closeInterface(getInterface());
        player.getSkills().sendSkillMenu(Enums.SKILL_GUIDES_ENUM.getKey(SkillConstants.SKILLS[currentSkill]).orElseThrow(RuntimeException::new), 0);
    }
    
    /**
     * Handles teleporting for the current skill
     */
    private void handleSkillTeleport(Player player) {
        if (currentSkill == -1) return;
        
        player.getInterfaceHandler().closeInterface(getInterface());
        
        // Get skill teleport data from SkillsTabInterface
        SkillTeleportData skillTeleport = SkillsTabInterface.getSkillTeleportData(currentSkill);
        
        if (player.isDebugging) {
            player.sendMessage("skill: " + currentSkill);
        }
        
        if (currentSkill == SkillConstants.FLETCHING) {
            player.sendMessage("There is no fletching location. You can fletch anywhere.");
            return;
        }
        
        if (skillTeleport != null) {
            if (skillTeleport.hasValidCoordinates()) {
                skillTeleport.teleport(player);
            } else {
                if (currentSkill == SkillConstants.ATTACK || currentSkill == SkillConstants.STRENGTH || 
                    currentSkill == SkillConstants.DEFENCE || currentSkill == SkillConstants.RANGED || 
                    currentSkill == SkillConstants.MAGIC || currentSkill == SkillConstants.HITPOINTS) {
                    final Category category = TeleportsList.getCategories().get("training teleports");
                    if (category != null) {
                        player.stopAll();
                        player.getVarManager().sendVar(261, category.getId());
                        player.getTeleportsManager().setSelectedCategory(category);
                        player.getTeleportsManager().attemptOpen();
                    }
                } else {
                    final Category category = TeleportsList.getCategories().get("skilling teleports");
                    if (category != null) {
                        player.stopAll();
                        player.getVarManager().sendVar(261, category.getId());
                        player.getTeleportsManager().setSelectedCategory(category);
                        player.getTeleportsManager().attemptOpen();
                    }
                }
            }
        } else {
            player.sendMessage("Teleport is not available for this skill.");
        }
    }
    
    @Override
    protected DefaultClickHandler getDefaultHandler() {
        return (player, componentId, slotId, itemId, optionId) -> {
            if (player.isLocked()) {
                return;
            }
            
            if (componentId == 4) { // View Skill Guide button
                handleSkillGuide(player);
            } else if (componentId == 6) { // Teleport button
                handleSkillTeleport(player);
            } else if (componentId == 8) { // Close button (if exists)
                player.getInterfaceHandler().closeInterface(getInterface());
            }
        };
    }
    
    @Override
    protected void attach() {
        // Interface setup if needed
    }
    
    @Override
    public void open(Player player) {
        player.getInterfaceHandler().sendInterface(this);
    }
    
    @Override
    protected void build() {
        // Interface building if needed
    }
    
    @Override
    public GameInterface getInterface() {
        // Using a simple interface ID - this might need to be adjusted based on available interfaces
        return GameInterface.CHATBOX; // Temporary - will need proper interface
    }
    
    /**
     * Inner class to hold skill teleport data (copied from SkillsTabInterface for access)
     */
    public static class SkillTeleportData {
        private final String skillName;
        private final int skillId;
        private final Location teleportLocation;

        public SkillTeleportData(String skillName, int skillId, Location teleportLocation) {
            this.skillName = skillName;
            this.skillId = skillId;
            this.teleportLocation = teleportLocation;
        }

        public String getSkillName() {
            return skillName;
        }

        public int getSkillId() {
            return skillId;
        }

        public Location getTeleportLocation() {
            return teleportLocation;
        }

        public boolean hasValidCoordinates() {
            return teleportLocation.getX() != 1 && teleportLocation.getY() != 1;
        }

        public void teleport(Player player) {
            // TODO: Add teleport animation and effects
            player.setLocation(teleportLocation);
            player.sendMessage("You have been teleported to the " + skillName + " training area.");
        }
    }
}
