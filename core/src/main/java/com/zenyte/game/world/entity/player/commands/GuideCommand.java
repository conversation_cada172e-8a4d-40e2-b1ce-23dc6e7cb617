package com.zenyte.game.world.entity.player.commands;

import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.world.entity.player.Skills;
import mgi.types.config.enums.Enums;

/**
 * Command to view skill guide for the currently selected skill
 */
public class GuideCommand implements Command {
    
    @Override
    public void execute(Player player, String[] args) {
        Object skillObj = player.getTemporaryAttributes().get("selected_skill_for_action");
        
        if (skillObj == null) {
            player.sendMessage("No skill selected. Please click on a skill first.");
            return;
        }
        
        int skill = (Integer) skillObj;
        
        // Clear the selected skill
        player.getTemporaryAttributes().remove("selected_skill_for_action");
        
        // Open the skill guide
        try {
            player.getSkills().sendSkillMenu(Enums.SKILL_GUIDES_ENUM.getKey(SkillConstants.SKILLS[skill]).orElseThrow(RuntimeException::new), 0);
            player.sendMessage("Opening " + Skills.getSkillName(skill) + " skill guide...");
        } catch (Exception e) {
            player.sendMessage("Could not open skill guide for " + Skills.getSkillName(skill));
        }
    }
    
    @Override
    public boolean canUse(Player player) {
        return true;
    }
}
