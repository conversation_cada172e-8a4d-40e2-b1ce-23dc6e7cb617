package com.zenyte.game.world.entity.player.commands;

import com.near_reality.cache.interfaces.teleports.Category;
import com.near_reality.cache.interfaces.teleports.TeleportsList;
import com.zenyte.game.model.ui.testinterfaces.SkillsTabInterface;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.world.entity.player.Skills;
import mgi.types.config.enums.Enums;

/**
 * Command handler for skill menu choices (1 for guide, 2 for teleport)
 */
public class SkillMenuCommand implements Command {
    
    @Override
    public void execute(Player player, String[] args) {
        if (args.length != 1) {
            return;
        }
        
        // Check if player has an active skill menu
        Object skillObj = player.getTemporaryAttributes().get("skill_menu_active");
        Boolean expectingChoice = (Boolean) player.getTemporaryAttributes().get("expecting_skill_choice");
        
        if (skillObj == null || expectingChoice == null || !expectingChoice) {
            return;
        }
        
        int skill = (Integer) skillObj;
        String choice = args[0];
        
        // Clear the menu state
        player.getTemporaryAttributes().remove("skill_menu_active");
        player.getTemporaryAttributes().remove("expecting_skill_choice");
        
        if ("1".equals(choice)) {
            // View Skill Guide
            handleSkillGuide(player, skill);
        } else if ("2".equals(choice)) {
            // Teleport
            handleSkillTeleport(player, skill);
        } else {
            player.sendMessage("Invalid choice. Please click on a skill again to see the menu.");
        }
    }
    
    /**
     * Handles viewing the skill guide for a given skill
     */
    private void handleSkillGuide(Player player, int skill) {
        player.getSkills().sendSkillMenu(Enums.SKILL_GUIDES_ENUM.getKey(SkillConstants.SKILLS[skill]).orElseThrow(RuntimeException::new), 0);
    }
    
    /**
     * Handles teleporting for a given skill
     */
    private void handleSkillTeleport(Player player, int skill) {
        SkillsTabInterface.SkillTeleportData skillTeleport = SkillsTabInterface.getSkillTeleportData(skill);
        
        if (player.isDebugging) {
            player.sendMessage("skill: " + skill);
        }
        
        if (skill == SkillConstants.FLETCHING) {
            player.sendMessage("There is no fletching location. You can fletch anywhere.");
            return;
        }
        
        if (skillTeleport != null) {
            if (skillTeleport.hasValidCoordinates()) {
                skillTeleport.teleport(player);
            } else {
                if (skill == SkillConstants.ATTACK || skill == SkillConstants.STRENGTH || 
                    skill == SkillConstants.DEFENCE || skill == SkillConstants.RANGED || 
                    skill == SkillConstants.MAGIC || skill == SkillConstants.HITPOINTS) {
                    final Category category = TeleportsList.getCategories().get("training teleports");
                    if (category != null) {
                        player.stopAll();
                        player.getVarManager().sendVar(261, category.getId());
                        player.getTeleportsManager().setSelectedCategory(category);
                        player.getTeleportsManager().attemptOpen();
                    }
                } else {
                    final Category category = TeleportsList.getCategories().get("skilling teleports");
                    if (category != null) {
                        player.stopAll();
                        player.getVarManager().sendVar(261, category.getId());
                        player.getTeleportsManager().setSelectedCategory(category);
                        player.getTeleportsManager().attemptOpen();
                    }
                }
            }
        } else {
            player.sendMessage("Teleport is not available for this skill.");
        }
    }
    
    @Override
    public boolean canUse(Player player) {
        return true;
    }
}
